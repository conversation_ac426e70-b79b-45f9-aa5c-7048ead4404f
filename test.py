
# from openai import AzureOpenAI
# from langchain.vectorstores import Mi<PERSON><PERSON><PERSON>



# def load_documents_knowledge(source_dir: str, secondary_directories: str) -> List[Document]:
#     """
#     Loads all documents from the source documents directory, ignoring specified files
#     """
#     all_files = []
#     for ext in LOADER_MAPPING:
#         all_files.extend(

#             glob.glob(os.path.join(source_dir, secondary_directories, f"**/*{ext}"), recursive=True)
#         )
#     filtered_files = [file_path for file_path in all_files if file_path]

#     with Pool(processes=os.cpu_count()) as pool:
#         results = []
#         with tqdm(total=len(filtered_files), desc='Loading new documents', ncols=80) as pbar:
#             for i, docs in enumerate(pool.imap_unordered(load_single_document, filtered_files)):
#                 results.extend(docs)
#                 pbar.update()

#     return results

# def process_documents_knowledge(secondary_directories: str) -> List[Document]:
#     """
#     加载文档并拆分为块
#     """
#     print(f"加载文件目录: {KNOWLEDGE_FOLDER}")
#     documents = load_documents_knowledge(KNOWLEDGE_FOLDER, secondary_directories)
#     if not documents:
#         print("没有文件需要加载")
#         exit(0)
#     print(f"加载 {len(documents)} 文件从 {KNOWLEDGE_FOLDER}")
#     text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
#     texts = text_splitter.split_documents(documents)
#     print(f"切割 {len(texts)} 文本块 (最大. {chunk_size} tokens 令牌)")
#     return texts

# collection_name = ''

# texts = process_documents_knowledge(collection_name)

# AZURE_OPENAI_API_KEY='8cef36400bd2'
# AZURE_OPENAI_ENDPOINT='https://openai-svw2.openai.azure.com/'
# AZURE_OPENAI_DEPLOYMENT='SVW-EMBEDDING-LARGE'
# model_name = "text-embedding-3-large"

# from langchain.embeddings import AzureOpenAIEmbeddings

# embeddings = AzureOpenAIEmbeddings(
#     azure_endpoint = AZURE_OPENAI_ENDPOINT,
#     deployment = AZURE_OPENAI_DEPLOYMENT,
#     openai_api_key = AZURE_OPENAI_API_KEY,
    
    
    
# )

# embeddings = AzureOpenAI(
#                 api_key=AZURE_OPENAI_API_KEY,
#                 api_version="2024-10-21",
#                 azure_deployment=AZURE_OPENAI_DEPLOYMENT,
#                 azure_endpoint=AZURE_OPENAI_ENDPOINT
#             )

# Milvus.from_documents(
#         texts,
#         collection_name=collection_name,
#         embedding=embeddings,
#         connection_args={"host": MILVUS_HOST, "port": MILVUS_PORT}
#     )






import datetime

a = [{'id': 'c2_128a9c80abd900f937a3004b24744bc8', 'agent_id': '007c529739690a861ad158e4237fea06', 'sys_created': datetime.datetime(2023, 7, 5, 19, 39, 26), 'sys_updated': datetime.datetime(2023, 7, 5, 19, 39, 26), 'created_user_id': 'EXT18941', 'created_user_name': 'EXT18941', 'last_edit_user_id': 'EXT18941', 'last_edit_user_name': 'EXT18941', 'last_edit_time': datetime.datetime(2023, 7, 5, 19, 39, 27), 'delete_flag': 0, 'op_lock': None, 'editing_user_id': None, 'editing_user_name': None, 'editing_status': 0, 'editing_start_time': datetime.datetime(1970, 1, 1, 0, 0), 'effect_time': datetime.datetime(2000, 1, 1, 0, 0), 'expire_time': datetime.datetime(2099, 12, 31, 23, 59, 59), 'version': 0.5, 'question': '超级APP的流量是如何计费的', 'md5': None, 'answer': '{"type":0,"list":[{"type":1,"text":"非常抱歉，我目前无法理解您的问题，您可以点击jjzrg，我们将为您安排人工坐席服务，谢谢！"}]}', 'dir_id': '6bc34ea44008ee32b87d4f55daec8f93', 'tags': '[]', 'kg_attribs': '[]', 'is_active_hook': 0, 'instruction': None, 'suggest': '[]', 'interrupted': 1, 'bpm_wk_inst_id': '-1', 'original_id': 'a2_c9b023198c340022466aa3dbe4a28d48', 'version_status': 1, 'edit_type': 2, 'pub_user_id': 'EXT18941', 'pub_user_name': 'EXT18941', 'pub_time': datetime.datetime(2023, 7, 5, 19, 39, 27), 'audit_id': 'b2_243580f919300d3d99ac2a22a1cc2589', 'source': 1, 'source_id': None, 'docs': '[]', 'attaches': '[]', 'recycle_status': 0, 'answer_plain_text': '非常抱歉，我目前无法理解您的问题，您可以点击jjzrg，我们将为您安排人工坐席服务，谢谢！\n\n', 'attach_plain_text': '', 'update_content': '同步', 'sync_source_agent': None}]

i = 0
for item in a:
    print(item.keys())
    break



补充下mysql中的字段：
1.doc表字段：
['id', 'agent_id', 'sys_created', 'sys_updated', 'created_user_id', 'created_user_name', 'last_edit_user_id', 'last_edit_user_name', 'last_edit_time', 'editing_user_id', 'editing_user_name', 'editing_start_time', 'editing_status', 'effect_time', 'expire_time', 'version', 'title', 'rich_text', 'plain_text', 'dir_id', 'attaches', 'faqs', 'tags', 'kg_attribs', 'word_num', 'picture_num', 'link_num', 'doc_url', 'md5', 'bpm_wk_inst_id', 'original_id', 'delete_flag', 'op_lock', 'digest', 'version_status', 'edit_type', 'pub_user_id', 'pub_user_name', 'pub_time', 'audit_id', 'docs', 'source_file_id', 'text_md5', 'source_type', 'recycle_status', 'attach_plain_text', 'channels_code', 'update_content', 'sync_source_agent']
2.faq表字段：
['id', 'agent_id', 'sys_created', 'sys_updated', 'created_user_id', 'created_user_name', 'last_edit_user_id', 'last_edit_user_name', 'last_edit_time', 'delete_flag', 'op_lock', 'editing_user_id', 'editing_user_name', 'editing_status', 'editing_start_time', 'effect_time', 'expire_time', 'version', 'question', 'md5', 'answer', 'dir_id', 'tags', 'kg_attribs', 'is_active_hook', 'instruction', 'suggest', 'interrupted', 'bpm_wk_inst_id', 'original_id', 'version_status', 'edit_type', 'pub_user_id', 'pub_user_name', 'pub_time', 'audit_id', 'source', 'source_id', 'docs', 'attaches', 'recycle_status', 'answer_plain_text', 'attach_plain_text', 'update_content', 'sync_source_agent']