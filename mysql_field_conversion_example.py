"""
MySQL查询结果字段转换示例
演示如何使用config.py中的转换函数处理MySQL查询结果
"""

from config import (
    doc_keys, faq_keys, 
    convert_mysql_result_to_dict, 
    convert_dict_cursor_result
)
from database.mysql import MySQLDatabase


def example_traditional_cursor_usage():
    """
    示例1: 使用传统游标查询并转换结果
    适用于使用pymysql.cursors.Cursor的情况
    """
    print("=== 示例1: 传统游标查询结果转换 ===")
    
    # 模拟传统游标查询结果（tuple格式）
    # 单行结果示例
    single_doc_result = (
        'doc_001', 'orig_001', 'parent_001', '测试文档标题', 
        'agent_001', '测试智能体', 'dir_001', 'orig_parent_001',
        '张三', '2024-01-01 10:00:00', '1.0', '标签1,标签2',
        '附件1.pdf', 1500, 3, 2, '文档正文内容...', 
        '<p>HTML格式内容</p>', '[测试智能体] 根目录/子目录', 2
    )
    
    # 转换单行结果
    converted_single = convert_mysql_result_to_dict(single_doc_result, doc_keys)
    print("单行文档结果转换:")
    for key, value in converted_single.items():
        print(f"  {key}: {value}")
    
    print("\n" + "-"*50 + "\n")
    
    # 多行结果示例
    multi_faq_results = [
        ('faq_001', 'agent_001', '测试智能体', '如何使用系统？', 
         '请参考用户手册...', 'dir_001', 'orig_faq_001', '李四', 
         '2024-01-01 11:00:00', 1, '[测试智能体] FAQ目录', 1),
        ('faq_002', 'agent_001', '测试智能体', '系统支持哪些功能？', 
         '系统支持文档管理...', 'dir_002', 'orig_faq_002', '王五', 
         '2024-01-01 12:00:00', 1, '[测试智能体] FAQ目录', 1)
    ]
    
    # 转换多行结果
    converted_multi = convert_mysql_result_to_dict(multi_faq_results, faq_keys)
    print("多行FAQ结果转换:")
    for i, faq_dict in enumerate(converted_multi):
        print(f"  FAQ {i+1}:")
        for key, value in faq_dict.items():
            print(f"    {key}: {value}")
        print()


def example_dict_cursor_usage():
    """
    示例2: 使用DictCursor查询并重新排序字段
    适用于使用pymysql.cursors.DictCursor的情况
    """
    print("=== 示例2: DictCursor查询结果重新排序 ===")
    
    # 模拟DictCursor查询结果（字典格式，字段顺序可能不一致）
    dict_doc_result = {
        'title': '测试文档标题',
        'id': 'doc_001',
        'agentName': '测试智能体',
        'docText': '文档正文内容...',
        'originalId': 'orig_001',
        'agentId': 'agent_001',
        'pubTime': '2024-01-01 10:00:00',
        'dirId': 'dir_001',
        'version': '1.0',
        'tags': '标签1,标签2',
        'pubUserName': '张三',
        'wordNum': 1500,
        'pictureNum': 3,
        'linkNum': 2,
        'attaches': '附件1.pdf',
        'docHtml': '<p>HTML格式内容</p>',
        'id_parent': 'parent_001',
        'originalId_parent': 'orig_parent_001',
        'dir_name': '[测试智能体] 根目录/子目录',
        'dir_level': 2
    }
    
    # 重新排序字段
    reordered_result = convert_dict_cursor_result(dict_doc_result, doc_keys)
    print("重新排序后的文档字段:")
    for key, value in reordered_result.items():
        print(f"  {key}: {value}")


def example_real_database_query():
    """
    示例3: 实际数据库查询示例
    """
    print("=== 示例3: 实际数据库查询 ===")
    
    try:
        # 使用DictCursor的数据库连接
        db = MySQLDatabase("cskb")
        
        # 查询文档数据（注意：字段顺序要与doc_keys匹配）
        doc_sql = """
        SELECT id, originalId, '' as id_parent, title, agentId, 
               '' as agentName, dirId, '' as originalId_parent, 
               pubUserName, pubTime, version, tags, attaches, 
               wordNum, pictureNum, linkNum, '' as docText, 
               '' as docHtml, '' as dir_name, 0 as dir_level
        FROM cskb_doc_pub 
        LIMIT 3
        """
        
        doc_results = db.execute_query(doc_sql)
        print(f"查询到 {len(doc_results)} 条文档记录")
        
        # 由于使用了DictCursor，结果已经是字典格式，只需重新排序
        if doc_results:
            reordered_docs = convert_dict_cursor_result(doc_results, doc_keys)
            print("第一条文档记录:")
            for key, value in reordered_docs[0].items():
                print(f"  {key}: {value}")
        
        db.close()
        
    except Exception as e:
        print(f"数据库查询示例执行失败: {e}")


def example_error_handling():
    """
    示例4: 错误处理和边界情况
    """
    print("=== 示例4: 错误处理示例 ===")
    
    # 测试空结果
    empty_result = convert_mysql_result_to_dict([], doc_keys)
    print(f"空列表转换结果: {empty_result}")
    
    # 测试字段数量不匹配的情况
    try:
        incomplete_result = ('doc_001', 'orig_001')  # 只有2个字段，但doc_keys有20个
        convert_mysql_result_to_dict(incomplete_result, doc_keys)
    except ValueError as e:
        print(f"字段数量不匹配错误: {e}")
    
    # 测试缺失字段的DictCursor结果
    incomplete_dict = {'id': 'doc_001', 'title': '测试文档'}
    reordered_incomplete = convert_dict_cursor_result(incomplete_dict, doc_keys)
    print("缺失字段的字典转换结果（缺失字段将为None）:")
    missing_fields = [k for k, v in reordered_incomplete.items() if v is None]
    print(f"  缺失的字段: {missing_fields[:5]}...")  # 只显示前5个


def practical_usage_tips():
    """
    实际使用建议
    """
    print("=== 实际使用建议 ===")
    print("""
    1. 选择合适的转换函数:
       - 使用传统Cursor: convert_mysql_result_to_dict()
       - 使用DictCursor: convert_dict_cursor_result()
    
    2. SQL查询建议:
       - 传统Cursor: 确保SELECT字段顺序与doc_keys/faq_keys一致
       - DictCursor: 字段顺序无关，但要确保字段名正确
    
    3. 错误处理:
       - 始终检查查询结果是否为空
       - 处理字段数量不匹配的情况
       - 对于可选字段，在SQL中使用默认值或NULL
    
    4. 性能考虑:
       - DictCursor稍慢但更安全
       - 传统Cursor更快但需要严格的字段顺序
    """)


if __name__ == "__main__":
    example_traditional_cursor_usage()
    print("\n" + "="*60 + "\n")
    
    example_dict_cursor_usage()
    print("\n" + "="*60 + "\n")
    
    example_real_database_query()
    print("\n" + "="*60 + "\n")
    
    example_error_handling()
    print("\n" + "="*60 + "\n")
    
    practical_usage_tips()
