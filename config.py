import pymysql

from env import ENVIRONMENT

DOC_EMBEDDING_START_OFFSET = 0      # 文档Embedding起始位置
DOC_EMBEDDING_END_OFFSET = 9999     # 文档Embedding结束位置

def dbinfo(db_name):
    if db_name == "cskb":
        if (ENVIRONMENT == "test"):
            # CSKB测试环境
            host="************"
            port=18306
            user="root"
            password="123456a?"
            database="cskb_1_0_work"
        else:
            # CKSB生产环境
            host="************"
            port=8306
            user="root"
            password="123456a?"
            database="cskb_1_0_work"
    else:
        if (ENVIRONMENT == "test"):
            # Copilot测试环境
            host="************"
            port=4512
            user="copilot"
            password="zJ$dZZp%8wI*"
            database="svwcopilot"
        else:
            # Copilot生产环境
            host="*************"
            port=4273
            user="copilot"
            password="gB8vyT_^6jW^"
            database="svwcopilot"

    return host,port,user,password,database


# 获取copilot数据库连接信息
host,port,user,password,database = dbinfo(db_name="copilot")


# 获取cskb_agent_key参数
def init_cskb_agent_key_dict():
    cskb_agent_key = {}

    db_connection = pymysql.connect(
        host=host,
        port=port,
        user=user,
        password=password,
        database=database
    )
    try:
        cursor = db_connection.cursor()
        print("### 获取cskb_agent_key参数")

        querySQL = """SELECT agent_id,agent_name,access_token,env FROM cskb_agent_key"""
        cursor.execute(querySQL)
        result = cursor.fetchall()
        # 提交事务
        db_connection.commit()

        for row in result:
            agent_id = row[0]
            agent_name = row[1]
            access_token = row[2]
            env = row[3]
            
            key = f'[{env}] {agent_name}'
            cskb_agent_key[key] = {"agent_id": agent_id, "access_token": access_token}
        
    except Exception as error:
        print(f"## 获取cskb_agent_key参数：数据库操作失败: {error}")

    finally:
        if db_connection:
            cursor.close()
            db_connection.close()
    return cskb_agent_key
CSKB_AGENT_KEY_DICT = init_cskb_agent_key_dict()


# 初始化各垂直领域知识库环境
KNOWLEDGE_FIELD_CONFIGS = {
    "EmpZero": 
        {
            "cskb_env":ENVIRONMENT,                      # CSKB知识库环境：test/prod
            "similarity_threshold":0.35,            # 文档知识召回置信度预置：openai-large：0.35  openai：0.76  m3e：-116
            "similarity_threshold_faq":0.6,         # FAQ知识召回置信度预置
            "chunks_adopt":3,                       # 向量召回知识片段数量
            "allow_internet_search": True,          # 是否允许互联网搜索
            "show_reference": True,                 # 是否显示参考文档  （Backup:若要开放小度测试，则EmpZero参数字典中的值改为False）
        },
    "SSIS": 
        {
            "cskb_env":"prod",
            "similarity_threshold":0.25,
            "similarity_threshold_faq":0.6,
            "chunks_adopt":3,
            "allow_internet_search": True,
            "show_reference": True,
        },
    "EDI-WEB": 
        { 
            "cskb_env":"prod",
            "similarity_threshold":0.15,
            "similarity_threshold_faq":0.6,
            "chunks_adopt":6,
            "allow_internet_search": False,
            "show_reference": False,
        },
    "RISE": 
        {
            "cskb_env":ENVIRONMENT,
            "similarity_threshold":0.25,
            "similarity_threshold_faq":0.6,
            "chunks_adopt":3,
            "allow_internet_search": True,
            "show_reference": True,
        },
    "WISECCP": 
        {
            "cskb_env":"prod",
            "similarity_threshold":0.25,
            "similarity_threshold_faq":0.6,
            "chunks_adopt":3,
            "allow_internet_search": False,
            "show_reference": True,
        },
    "D-FMEA": 
        {
            "cskb_env":"prod",
            "similarity_threshold":0.25,
            "similarity_threshold_faq":0.6,
            "chunks_adopt":3,
            "allow_internet_search": False,
            "show_reference": True,
        },
    "XIAODU": 
        {
            "cskb_env":"prod",
            "similarity_threshold":0.25,
            "similarity_threshold_faq":0.6,
            "chunks_adopt":3,
            "allow_internet_search": True,
            "show_reference": True,
        },
}

# CSKB后端服务地址
CSKB_ADDRESS_DICT = {
    "test": {
        "cskb_url" : "http://************:9673",  # 测试环境
        "cskb_attach_download_url": "http://************:8563"  #测试环境 - 附件下载
    },
    "prod":{
        "cskb_url" : "http://************:8573",   #生产环境
        "cskb_attach_download_url" : "http://************:8563"  #生产环境 - 附件下载
    }
}



# 知识领域：将KNOWLEDGE_FIELD_CSKB_ENV转化为数组
KNOWLEDGE_FIELDS = [key for key,value in KNOWLEDGE_FIELD_CONFIGS.items()]
KNOWLEDGE_FIELD_CSKB_ENV = {key: value["cskb_env"] for key, value in KNOWLEDGE_FIELD_CONFIGS.items() if "cskb_env" in value}


def cskb_agent_info(knowledge_field:str):
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]

    def cskb_agent_setting(agent_name:str, user_id:str, load_attach:bool):
        cskb_key = f"[{cskb_env}] {agent_name}"
        if cskb_key in CSKB_AGENT_KEY_DICT.keys():
            cskb_value = CSKB_AGENT_KEY_DICT[cskb_key]
            return {"name": agent_name, "token": cskb_value['access_token'], "agentId": cskb_value['agent_id'], "user_id": user_id, "load_attach": load_attach}
        else:
            print (f"## 未找到{cskb_key}的CSKB知识库信息")
            return {"name": agent_name, "token": "", "agentId": "", "user_id": user_id, "load_attach": load_attach}
    
    token_list = []
    if (knowledge_field == "SSIS"):
        token_list.append(cskb_agent_setting("零号员工知识库", "SSIS", False))  # [生产环境] SSIS安全管理

    elif (knowledge_field == "EDI-WEB"):
        token_list.append(cskb_agent_setting("零号员工知识库", "EDI-WEB", False))  # [生产环境] EDI-WEB物流管理

    elif (knowledge_field == "RISE") and cskb_env == "test":
        token_list.append(cskb_agent_setting("RiSE谈判助手知识库-test", "67764", True))  # [测试环境] RiSE谈判助手

    elif (knowledge_field == "RISE") and cskb_env == "prod":
        token_list.append(cskb_agent_setting("RiSE谈判助手知识库", "67764", True))  # [生产环境] RiSE谈判助手

    elif (knowledge_field == "WISECCP"):
        token_list.append(cskb_agent_setting("智慧党建知识库", "", False))  # [生产环境] 智慧党建

    elif (knowledge_field == "XIAODU"):
        token_list.append(cskb_agent_setting("上汽大众客服知识库", "18333", False))  # [生产环境] 智慧党建

    elif (knowledge_field == "D-FMEA"):
        token_list.append(cskb_agent_setting("研发知识库", "67764", False))

    elif (knowledge_field == "EmpZero") and cskb_env == "test":
        token_list.append(cskb_agent_setting("知识库测试", "67764", False))  # [测试环境] 零号员工
        
    else:
        token_list.append(cskb_agent_setting("零号员工知识库", "", False))  # [生产环境] 零号员工
        token_list.append(cskb_agent_setting("项目知了知识库", "", False))  # [生产环境] 项目知了
        token_list.append(cskb_agent_setting("研发知识库", "", False))  # [生产环境] 研发知识库

    return token_list


doc_keys = ['id', 'originalId', 'id_parent', 'title', 'agentId', 'agentName', 'dirId', 'originalId_parent', 'pubUserName', 'pubTime', 'version', 'tags', 'attaches', 'wordNum', 'pictureNum', 'linkNum', 'docText', 'docHtml', 'dir_name', 'dir_level']


if __name__ == "__main__":
    print(cskb_agent_info("EDI-WEB"))