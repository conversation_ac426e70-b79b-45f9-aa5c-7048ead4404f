"""
数据映射和转换工具模块
处理MySQL查询结果到标准字典格式的转换，支持字段名映射
"""

from typing import Dict, List, Union, Any, Optional
import logging

logger = logging.getLogger(__name__)


# 文档字段映射配置：MySQL字段名 -> 目标字段名
DOC_FIELD_MAPPING = {
    # 基础字段
    'id': 'id',
    'original_id': 'originalId',  # MySQL: original_id -> 目标: originalId
    'parent_id': 'id_parent',     # MySQL: parent_id -> 目标: id_parent
    'title': 'title',
    'agent_id': 'agentId',        # MySQL: agent_id -> 目标: agentId
    'agent_name': 'agentName',    # MySQL: agent_name -> 目标: agentName
    'dir_id': 'dirId',            # MySQL: dir_id -> 目标: dirId
    'original_parent_id': 'originalId_parent',  # MySQL: original_parent_id -> 目标: originalId_parent
    'pub_user_name': 'pubUserName',  # MySQL: pub_user_name -> 目标: pubUserName
    'pub_time': 'pubTime',        # MySQL: pub_time -> 目标: pubTime
    'version': 'version',
    'tags': 'tags',
    'attaches': 'attaches',
    'word_num': 'wordNum',        # MySQL: word_num -> 目标: wordNum
    'picture_num': 'pictureNum',  # MySQL: picture_num -> 目标: pictureNum
    'link_num': 'linkNum',        # MySQL: link_num -> 目标: linkNum
    'doc_text': 'docText',        # MySQL: doc_text -> 目标: docText
    'doc_html': 'docHtml',        # MySQL: doc_html -> 目标: docHtml
    'directory_name': 'dir_name', # MySQL: directory_name -> 目标: dir_name
    'directory_level': 'dir_level', # MySQL: directory_level -> 目标: dir_level
}

# FAQ字段映射配置：MySQL字段名 -> 目标字段名
FAQ_FIELD_MAPPING = {
    'id': 'id',
    'agent_id': 'agentId',
    'agent_name': 'agentName',
    'question': 'question',
    'answer': 'answer',
    'dir_id': 'dirId',
    'original_id': 'originalId',
    'pub_user_name': 'pubUserName',
    'pub_time': 'pubTime',
    'version_status': 'versionStatus',  # MySQL: version_status -> 目标: versionStatus
    'directory_name': 'dir_name',
    'directory_level': 'dir_level',
}

# 目标字段顺序定义
DOC_TARGET_FIELDS = [
    'id', 'originalId', 'id_parent', 'title', 'agentId', 'agentName', 
    'dirId', 'originalId_parent', 'pubUserName', 'pubTime', 'version', 
    'tags', 'attaches', 'wordNum', 'pictureNum', 'linkNum', 'docText', 
    'docHtml', 'dir_name', 'dir_level'
]

FAQ_TARGET_FIELDS = [
    'id', 'agentId', 'agentName', 'question', 'answer', 'dirId', 
    'originalId', 'pubUserName', 'pubTime', 'versionStatus', 
    'dir_name', 'dir_level'
]


class DataMapper:
    """数据映射器类，处理MySQL查询结果到标准格式的转换"""
    
    def __init__(self):
        self.doc_mapping = DOC_FIELD_MAPPING
        self.faq_mapping = FAQ_FIELD_MAPPING
        self.doc_fields = DOC_TARGET_FIELDS
        self.faq_fields = FAQ_TARGET_FIELDS
    
    def map_doc_result(self, mysql_result: Union[Dict, List[Dict]], 
                       use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
        """
        映射文档查询结果
        
        Args:
            mysql_result: MySQL查询结果
            use_dict_cursor: 是否使用DictCursor查询的结果
        
        Returns:
            映射后的标准格式字典或字典列表
        """
        return self._map_result(
            mysql_result, 
            self.doc_mapping, 
            self.doc_fields, 
            use_dict_cursor,
            "doc"
        )
    
    def map_faq_result(self, mysql_result: Union[Dict, List[Dict]], 
                       use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
        """
        映射FAQ查询结果
        
        Args:
            mysql_result: MySQL查询结果
            use_dict_cursor: 是否使用DictCursor查询的结果
        
        Returns:
            映射后的标准格式字典或字典列表
        """
        return self._map_result(
            mysql_result, 
            self.faq_mapping, 
            self.faq_fields, 
            use_dict_cursor,
            "faq"
        )
    
    def _map_result(self, mysql_result: Union[Dict, List[Dict]], 
                    field_mapping: Dict[str, str], 
                    target_fields: List[str],
                    use_dict_cursor: bool,
                    data_type: str) -> Union[Dict, List[Dict]]:
        """
        内部映射方法
        
        Args:
            mysql_result: MySQL查询结果
            field_mapping: 字段映射配置
            target_fields: 目标字段列表
            use_dict_cursor: 是否使用DictCursor
            data_type: 数据类型标识
        
        Returns:
            映射后的结果
        """
        if not mysql_result:
            return [] if isinstance(mysql_result, list) else {}
        
        if use_dict_cursor:
            return self._map_dict_result(mysql_result, field_mapping, target_fields)
        else:
            return self._map_tuple_result(mysql_result, field_mapping, target_fields)
    
    def _map_dict_result(self, dict_result: Union[Dict, List[Dict]], 
                         field_mapping: Dict[str, str], 
                         target_fields: List[str]) -> Union[Dict, List[Dict]]:
        """映射DictCursor查询结果"""
        def map_single_dict(source_dict: Dict) -> Dict:
            mapped_dict = {}
            
            # 首先按照映射关系转换字段
            for mysql_field, target_field in field_mapping.items():
                if mysql_field in source_dict:
                    mapped_dict[target_field] = source_dict[mysql_field]
            
            # 然后按照目标字段顺序组织结果，确保所有字段都存在
            ordered_dict = {}
            for field in target_fields:
                ordered_dict[field] = mapped_dict.get(field, None)
            
            return ordered_dict
        
        if isinstance(dict_result, list):
            return [map_single_dict(row) for row in dict_result]
        else:
            return map_single_dict(dict_result)
    
    def _map_tuple_result(self, tuple_result: Union[tuple, List[tuple]], 
                          field_mapping: Dict[str, str], 
                          target_fields: List[str]) -> Union[Dict, List[Dict]]:
        """映射传统Cursor查询结果（tuple格式）"""
        # 获取MySQL字段顺序（需要在SQL查询时保证顺序）
        mysql_fields = list(field_mapping.keys())
        
        def map_single_tuple(row: tuple) -> Dict:
            if len(row) != len(mysql_fields):
                logger.warning(f"字段数量不匹配: 查询结果有{len(row)}个字段，期望{len(mysql_fields)}个字段")
            
            # 先将tuple转换为临时字典
            temp_dict = {}
            for i, mysql_field in enumerate(mysql_fields):
                if i < len(row):
                    target_field = field_mapping[mysql_field]
                    temp_dict[target_field] = row[i]
            
            # 然后按照目标字段顺序组织结果
            ordered_dict = {}
            for field in target_fields:
                ordered_dict[field] = temp_dict.get(field, None)
            
            return ordered_dict
        
        if isinstance(tuple_result, list):
            return [map_single_tuple(row) for row in tuple_result]
        else:
            return map_single_tuple(tuple_result)
    
    def get_doc_sql_fields(self, use_mapping: bool = True) -> str:
        """
        获取文档查询的SQL字段列表
        
        Args:
            use_mapping: 是否使用字段映射（True返回MySQL字段名，False返回目标字段名）
        
        Returns:
            SQL字段列表字符串
        """
        if use_mapping:
            return ", ".join(self.doc_mapping.keys())
        else:
            return ", ".join(self.doc_fields)
    
    def get_faq_sql_fields(self, use_mapping: bool = True) -> str:
        """
        获取FAQ查询的SQL字段列表
        
        Args:
            use_mapping: 是否使用字段映射
        
        Returns:
            SQL字段列表字符串
        """
        if use_mapping:
            return ", ".join(self.faq_mapping.keys())
        else:
            return ", ".join(self.faq_fields)


# 创建全局实例
data_mapper = DataMapper()

# 便捷函数
def map_doc_result(mysql_result: Union[Dict, List[Dict]], 
                   use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
    """映射文档查询结果的便捷函数"""
    return data_mapper.map_doc_result(mysql_result, use_dict_cursor)

def map_faq_result(mysql_result: Union[Dict, List[Dict]], 
                   use_dict_cursor: bool = True) -> Union[Dict, List[Dict]]:
    """映射FAQ查询结果的便捷函数"""
    return data_mapper.map_faq_result(mysql_result, use_dict_cursor)

def get_doc_sql_fields(use_mapping: bool = True) -> str:
    """获取文档SQL字段列表的便捷函数"""
    return data_mapper.get_doc_sql_fields(use_mapping)

def get_faq_sql_fields(use_mapping: bool = True) -> str:
    """获取FAQ SQL字段列表的便捷函数"""
    return data_mapper.get_faq_sql_fields(use_mapping)
