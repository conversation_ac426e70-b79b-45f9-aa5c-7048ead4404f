"""
更新后的数据映射器使用示例
基于实际的MySQL表字段结构演示字段映射功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_mapper import (
    map_doc_result, 
    map_faq_result,
    get_doc_sql_fields,
    get_faq_sql_fields,
    DOC_FIELD_MAPPING,
    FAQ_FIELD_MAPPING,
    DOC_TARGET_FIELDS,
    FAQ_TARGET_FIELDS
)


def example_actual_mysql_fields():
    """
    示例1: 基于实际MySQL表字段的映射
    """
    print("=== 示例1: 实际MySQL表字段映射 ===")
    
    # 模拟实际的MySQL doc表查询结果
    mysql_doc_result = {
        'id': 'doc_001',
        'agent_id': 'agent_001',
        'title': '测试文档标题',
        'plain_text': '这是文档的纯文本内容...',
        'rich_text': '<p>这是文档的富文本内容</p>',
        'dir_id': 'dir_001',
        'pub_user_name': '张三',
        'pub_time': '2024-01-01 10:00:00',
        'version': '1.0',
        'tags': '标签1,标签2',
        'attaches': 'file1.pdf,file2.docx',
        'word_num': 1500,
        'picture_num': 3,
        'link_num': 2,
        'original_id': 'orig_001',
        'version_status': 1,
        'sys_created': '2024-01-01 09:00:00',
        'sys_updated': '2024-01-01 10:00:00',
        'created_user_name': '李四',
        'last_edit_user_name': '王五',
        'delete_flag': 0,
        'recycle_status': 0
    }
    
    print("原始MySQL查询结果（部分字段）:")
    key_fields = ['id', 'agent_id', 'title', 'plain_text', 'rich_text', 'pub_user_name']
    for key in key_fields:
        if key in mysql_doc_result:
            print(f"  {key}: {mysql_doc_result[key]}")
    
    # 使用映射器转换（只转换核心字段）
    mapped_result = map_doc_result(mysql_doc_result, use_dict_cursor=True, use_full_fields=False)
    
    print("\n映射后的标准格式（核心字段）:")
    key_mapped_fields = ['id', 'agentId', 'title', 'docText', 'docHtml', 'pubUserName']
    for key in key_mapped_fields:
        if key in mapped_result:
            print(f"  {key}: {mapped_result[key]}")
    
    print("\n字段映射关系:")
    print("  agent_id -> agentId")
    print("  plain_text -> docText")
    print("  rich_text -> docHtml")
    print("  pub_user_name -> pubUserName")


def example_full_fields_mapping():
    """
    示例2: 完整字段映射
    """
    print("=== 示例2: 完整字段映射 ===")
    
    # 模拟FAQ表查询结果
    mysql_faq_result = {
        'id': 'faq_001',
        'agent_id': 'agent_001',
        'question': '如何使用系统？',
        'answer': '请参考用户手册进行操作...',
        'dir_id': 'dir_001',
        'original_id': 'orig_faq_001',
        'pub_user_name': '李四',
        'pub_time': '2024-01-01 11:00:00',
        'version_status': 1,
        'tags': 'FAQ,帮助',
        'version': '1.0',
        'sys_created': '2024-01-01 10:30:00',
        'sys_updated': '2024-01-01 11:00:00',
        'created_user_name': '张三',
        'last_edit_user_name': '王五',
        'answer_plain_text': '请参考用户手册进行操作...',
        'delete_flag': 0,
        'recycle_status': 0
    }
    
    print("使用完整字段映射:")
    
    # 使用完整字段映射
    full_mapped = map_faq_result(mysql_faq_result, use_dict_cursor=True, use_full_fields=True)
    
    print("映射后的完整字段（部分显示）:")
    display_fields = ['id', 'agentId', 'question', 'answer', 'versionStatus', 
                     'sysCreated', 'createdUserName', 'answerPlainText']
    for key in display_fields:
        if key in full_mapped:
            print(f"  {key}: {full_mapped[key]}")


def example_sql_generation_with_real_fields():
    """
    示例3: 基于实际字段的SQL生成
    """
    print("=== 示例3: 基于实际字段的SQL生成 ===")
    
    # 获取MySQL字段列表
    doc_mysql_fields = get_doc_sql_fields(use_mapping=True)
    print("文档表MySQL字段列表（前10个）:")
    fields_list = doc_mysql_fields.split(', ')
    for i, field in enumerate(fields_list[:10]):
        print(f"  {i+1}. {field}")
    print(f"  ... 总共 {len(fields_list)} 个字段")
    
    # 生成实际可用的SQL查询
    core_fields = [
        'id', 'agent_id', 'title', 'plain_text', 'rich_text', 
        'dir_id', 'pub_user_name', 'pub_time', 'version', 
        'tags', 'attaches', 'word_num', 'picture_num', 'link_num',
        'original_id', 'version_status'
    ]
    
    sql_template = f"""
    SELECT {', '.join(core_fields)}
    FROM cskb_doc_pub 
    WHERE agent_id = %s 
    AND version_status = 1 
    AND delete_flag = 0
    ORDER BY pub_time DESC
    LIMIT 10
    """
    
    print(f"\n生成的实际SQL查询:")
    print(sql_template)


def example_batch_processing_real_data():
    """
    示例4: 批量处理实际数据格式
    """
    print("=== 示例4: 批量处理实际数据格式 ===")
    
    # 模拟多条实际的MySQL查询结果
    mysql_results = [
        {
            'id': 'doc_001',
            'agent_id': 'agent_001',
            'title': '用户手册第一章',
            'plain_text': '本章介绍系统基本功能...',
            'rich_text': '<h1>用户手册第一章</h1><p>本章介绍系统基本功能...</p>',
            'pub_user_name': '张三',
            'pub_time': '2024-01-01 10:00:00',
            'version_status': 1,
            'word_num': 800,
        },
        {
            'id': 'doc_002',
            'agent_id': 'agent_001',
            'title': '用户手册第二章',
            'plain_text': '本章介绍高级功能...',
            'rich_text': '<h1>用户手册第二章</h1><p>本章介绍高级功能...</p>',
            'pub_user_name': '李四',
            'pub_time': '2024-01-02 10:00:00',
            'version_status': 1,
            'word_num': 1200,
        }
    ]
    
    print(f"批量处理 {len(mysql_results)} 条记录:")
    
    # 批量映射
    mapped_results = map_doc_result(mysql_results, use_dict_cursor=True, use_full_fields=False)
    
    for i, doc in enumerate(mapped_results):
        print(f"  文档 {i+1}:")
        print(f"    id: {doc['id']}")
        print(f"    title: {doc['title']}")
        print(f"    agentId: {doc['agentId']}")  # 注意字段名已转换
        print(f"    pubUserName: {doc['pubUserName']}")  # pub_user_name -> pubUserName
        print(f"    wordNum: {doc['wordNum']}")  # word_num -> wordNum


def example_error_handling_and_validation():
    """
    示例5: 错误处理和数据验证
    """
    print("=== 示例5: 错误处理和数据验证 ===")
    
    # 测试缺失字段的情况
    incomplete_data = {
        'id': 'doc_001',
        'title': '不完整的文档数据'
        # 缺少其他字段
    }
    
    print("处理不完整的数据:")
    try:
        mapped_incomplete = map_doc_result(incomplete_data, use_dict_cursor=True)
        print("映射成功，缺失字段将设为None:")
        print(f"  id: {mapped_incomplete['id']}")
        print(f"  title: {mapped_incomplete['title']}")
        print(f"  agentId: {mapped_incomplete['agentId']}")  # 应该是None
        print(f"  docText: {mapped_incomplete['docText']}")  # 应该是None
    except Exception as e:
        print(f"映射失败: {e}")
    
    # 测试类型错误
    print("\n测试类型错误:")
    try:
        # 尝试用DictCursor模式处理元组数据
        tuple_data = ('doc_001', 'agent_001', '标题')
        map_doc_result(tuple_data, use_dict_cursor=True)
    except TypeError as e:
        print(f"类型错误（预期）: {e}")


def example_integration_recommendations():
    """
    示例6: 项目集成建议
    """
    print("=== 示例6: 项目集成建议 ===")
    
    print("""
    实际项目集成建议:
    
    1. 数据访问层集成:
       - 在database/mysql.py中使用映射器
       - 统一处理所有查询结果的字段转换
    
    2. 字段映射配置:
       - 根据实际表结构调整DOC_FIELD_MAPPING和FAQ_FIELD_MAPPING
       - 添加新字段时同步更新映射配置
    
    3. SQL查询优化:
       - 只查询需要的字段，避免SELECT *
       - 使用get_doc_sql_fields()生成字段列表
    
    4. 性能考虑:
       - 对于只需要核心字段的场景，使用use_full_fields=False
       - 对于需要完整数据的场景，使用use_full_fields=True
    
    5. 代码组织:
       - utils/data_mapper.py: 映射配置和核心逻辑
       - database/: 数据访问层，使用映射器
       - services/: 业务逻辑层，使用映射后的标准数据
    
    6. 扩展字段处理:
       - agentName, dir_name, dir_level等虚拟字段需要额外查询
       - 可以在业务层补充这些字段的值
    """)


if __name__ == "__main__":
    example_actual_mysql_fields()
    print("\n" + "="*60 + "\n")
    
    example_full_fields_mapping()
    print("\n" + "="*60 + "\n")
    
    example_sql_generation_with_real_fields()
    print("\n" + "="*60 + "\n")
    
    example_batch_processing_real_data()
    print("\n" + "="*60 + "\n")
    
    example_error_handling_and_validation()
    print("\n" + "="*60 + "\n")
    
    example_integration_recommendations()
